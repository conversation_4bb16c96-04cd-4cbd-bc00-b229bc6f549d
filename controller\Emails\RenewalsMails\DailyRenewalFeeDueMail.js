require("dotenv").config();
const nodemailer = require("nodemailer");
const cron = require("node-cron");
const RenewalFeeData = require("../../../models/RenewalFeeData");
const Applications = require("../../../models/Applications");
const sequelize = require("../../../utils/database");
const { Op } = require("sequelize");
const XLSX = require("xlsx");
const fs = require("fs");
const { format } = require("date-fns");

// Email Configuration
const transporter = nodemailer.createTransport({
  host: process.env.EMAIL_HOST,
  port: process.env.EMAIL_PORT,
  secure: false,
  auth: {
    user: process.env.EMAIL_USER,
    pass: process.env.EMAIL_PASS,
  },
});

function formatDate(value) {
  if (!value) return "N/A";
  const dateValue = new Date(value);
  return isNaN(dateValue.getTime()) ? value : format(dateValue, "dd-MM-yyyy");
}

function extractYearNumber(yearString) {
  const match = yearString.match(/\d+/); // Extract numeric part
  return match ? parseInt(match[0], 10) : 0; // Convert to integer
}

async function sendDailyRenewalFeeEmail() {
  try {
    await sequelize.authenticate();
    console.log("Database connected successfully.");

    const today = new Date();
    const startOfDay = new Date(
      today.getFullYear(),
      today.getMonth(),
      today.getDate()
    );
    const endOfDay = new Date(
      today.getFullYear(),
      today.getMonth(),
      today.getDate(),
      23,
      59,
      59
    );

    console.log("Fetching renewal records for:", formatDate(startOfDay));

    // Fetch normal renewal applications (due today)
    const normalApplicationsData = await Applications.findAll({
      where: {
        due_date_of_next_renewal: {
          [Op.between]: [startOfDay, endOfDay],
        },
        // due_date_of_next_renewal: "2025-02-16",
        legal_patent_status: {
          [Op.ne]: "Under Extension Period", // Not equal to "Under Extension Period"
        },
      },
      attributes: [
        "application_number",
        "patent_number",
        "applicant_name",
        "due_date_of_next_renewal",
        "due_date_with_extension",
        "legal_patent_status",
      ],
      order: [["due_date_of_next_renewal", "ASC"]],
    });

    // Fetch extension period applications (due today on due_date_with_extension)
    console.log("Looking for extension period applications with extension due date:", formatDate(startOfDay));
    const extensionApplicationsData = await Applications.findAll({
      where: {
        legal_patent_status: "Under Extension Period",
        // due_date_with_extension: "2025-02-16",
        // due_date_with_extension: {
        //   [Op.between]: [startOfDay, endOfDay],
        // },
      },
      attributes: [
        "application_number",
        "patent_number",
        "applicant_name",
        "due_date_of_next_renewal",
        "due_date_with_extension",
        "legal_patent_status",
      ],
      order: [["due_date_with_extension", "ASC"]],
    });

    console.log(`Found ${normalApplicationsData.length} normal renewal applications`);
    console.log(`Found ${extensionApplicationsData.length} extension period applications`);

    if (normalApplicationsData.length === 0 && extensionApplicationsData.length === 0) {
      console.log("No renewals due today.");

      // Prepare Email Content for no dues
      let emailContent = "<h2>Patent Renewal Fees Due Today</h2>";
      emailContent += `<h3>Date: ${formatDate(today)}</h3>`;
      emailContent +=
        "<p style='font-size: 16px; color: #666; margin: 20px 0;'><strong>No patent renewals are due today.</strong></p>";

      // Send Email
      const mailOptions = {
        from: process.env.EMAIL_FROM,
        to: process.env.EMAIL_TO,
        subject: `Patent Renewal Fees Due Today - ${formatDate(today)}`,
        html: emailContent,
      };

      const info = await transporter.sendMail(mailOptions);
      console.log("Daily renewal fee email sent successfully:", info.messageId);
      return;
    }



    // Create table HTML function for normal applications
    const createNormalTableHTML = (title) => {
      return `
        <h3>${title}</h3>
        <table border="1" style="border-collapse: collapse; width: 100%; margin-bottom: 20px;">
          <tr>
            <th>Application Number</th>
            <th>Patent Number</th>
            <th>Applicant Name</th>
            <th>Legal Status</th>
            <th>Normal Due Date</th>
            <th>Due Date with Extension</th>
          </tr>
      `;
    };

    // Create table HTML function for extension period applications
    const createExtensionTableHTML = (title) => {
      return `
        <h3>${title}</h3>
        <table border="1" style="border-collapse: collapse; width: 100%; margin-bottom: 20px;">
          <tr>
            <th>Application Number</th>
            <th>Patent Number</th>
            <th>Applicant Name</th>
            <th>Legal Status</th>
            <th>Normal Due Date</th>
            <th>Due Date with Extension</th>
          </tr>
      `;
    };

    // Use the separately fetched data
    const normalEntries = normalApplicationsData;
    const extensionEntries = extensionApplicationsData;

    // Prepare Email Content
    let emailContent = "<h2>Patent Renewal Fees Due Today</h2>";
    emailContent += `<h3>Date: ${formatDate(today)}</h3>`;

    // Create normal entries table
    if (normalEntries.length > 0) {
      emailContent += createNormalTableHTML("Normal Renewal Due Applications");
      normalEntries.forEach((app) => {
        emailContent += `<tr>
          <td>${app.application_number}</td>
          <td>${app.patent_number || "N/A"}</td>
          <td>${app.applicant_name || "N/A"}</td>
          <td>${app.legal_patent_status || "N/A"}</td>
          <td>${formatDate(app.due_date_of_next_renewal)}</td>
          <td>${formatDate(app.due_date_with_extension)}</td>
        </tr>`;
      });
      emailContent += "</table>";
    }

    // Create extension period table
    if (extensionEntries.length > 0) {
      emailContent += createExtensionTableHTML("Applications Under Extension Period");
      extensionEntries.forEach((app) => {
        emailContent += `<tr>
          <td>${app.application_number}</td>
          <td>${app.patent_number || "N/A"}</td>
          <td>${app.applicant_name || "N/A"}</td>
          <td>${app.legal_patent_status || "N/A"}</td>
          <td>${formatDate(app.due_date_of_next_renewal)}</td> // Normal Due Date
          <td>${formatDate(app.due_date_with_extension)}</td>
        </tr>`;
      });
      emailContent += "</table>";
    }

    // Create Excel files
    const currentDate = formatDate(today).replace(/-/g, "_");
    const attachments = [];

    // Create Excel for normal entries
    if (normalEntries.length > 0) {
      const normalExcelPath = `./Normal_Renewal_Due_Applications_${currentDate}.xlsx`;
      const normalWorkbook = XLSX.utils.book_new();
      const normalSheetData = [
        [
          "Application Number",
          "Patent Number",
          "Applicant Name",
          "Legal Status",
          "Normal Due Date",
          "Due Date with Extension", // Extension Due Date
        ],
        ...normalEntries.map((app) => [
          app.application_number,
          app.patent_number || "N/A",
          app.applicant_name || "N/A",
          app.legal_patent_status || "N/A",
          formatDate(app.due_date_of_next_renewal),
          formatDate(app.due_date_with_extension), // Extension Due Date
        ]),
      ];

      const normalWorksheet = XLSX.utils.aoa_to_sheet(normalSheetData);
      XLSX.utils.book_append_sheet(
        normalWorkbook,
        normalWorksheet,
        "Normal Renewals" // Shortened sheet name
      );
      XLSX.writeFile(normalWorkbook, normalExcelPath);
      attachments.push({
        filename: `Normal_Renewal_Due_${currentDate}.xlsx`,
        path: normalExcelPath,
        contentType:
          "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
      });
    }

    // Create Excel for extension entries
    if (extensionEntries.length > 0) {
      const extensionExcelPath = `./Extension_Period_Applications_${currentDate}.xlsx`;
      const extensionWorkbook = XLSX.utils.book_new();
      const extensionSheetData = [
        [
          "Application Number",
          "Patent Number",
          "Applicant Name",
          "Legal Status",
          "Normal Due Date",
          "Due Date of Extension",
        ],
        ...extensionEntries.map((app) => [
          app.application_number,
          app.patent_number || "N/A",
          app.applicant_name || "N/A",
          app.legal_patent_status || "N/A",
          formatDate(app.due_date_of_next_renewal), // Normal Due Date
          formatDate(app.due_date_with_extension),
        ]),
      ];

      const extensionWorksheet = XLSX.utils.aoa_to_sheet(extensionSheetData);
      XLSX.utils.book_append_sheet(
        extensionWorkbook,
        extensionWorksheet,
        "Extension Period" // Shortened sheet name
      );
      XLSX.writeFile(extensionWorkbook, extensionExcelPath);
      attachments.push({
        filename: `Extension_Period_${currentDate}.xlsx`,
        path: extensionExcelPath,
        contentType:
          "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
      });
    }

    // Send Email
    const mailOptions = {
      from: process.env.EMAIL_FROM,
      to: process.env.EMAIL_TO,
      subject: `Patent Renewal Fees Due Today - ${formatDate(today)}`,
      html: emailContent,
      attachments: attachments,
    };

    const info = await transporter.sendMail(mailOptions);
    console.log("Daily renewal fee email sent successfully:", info.messageId);

    // Clean up: Delete the temporary Excel files
    attachments.forEach((attachment) => {
      fs.unlinkSync(attachment.path);
    });
  } catch (error) {
    console.error("Error sending daily renewal fee email:", error);
  }
}

// Schedule to run daily at 9 AM IST
const ScheduleDailyRenewalFeeEmail = () => {
  console.log("Scheduled daily renewal fee email job running at 9 AM IST.");
  cron.schedule(
    "0 9 * * *", // Runs at 9 AM daily
    async () => {
      try {
        await sendDailyRenewalFeeEmail();
        console.log("Daily renewal fee email job completed.");
      } catch (error) {
        console.error("Error in scheduled daily renewal fee email job:", error);
      }
    },
    {
      timezone: "Asia/Kolkata",
    }
  );
};

// For testing
sendDailyRenewalFeeEmail();

module.exports = {
  ScheduleDailyRenewalFeeEmail,
  // sendDailyRenewalFeeEmail,
};
