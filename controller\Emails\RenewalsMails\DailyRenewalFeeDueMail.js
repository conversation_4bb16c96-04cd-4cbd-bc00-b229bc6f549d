require("dotenv").config();
const nodemailer = require("nodemailer");
const cron = require("node-cron");
const RenewalFeeData = require("../../../models/RenewalFeeData");
const Applications = require("../../../models/Applications");
const sequelize = require("../../../utils/database");
const { Op } = require("sequelize");
const XLSX = require("xlsx");
const fs = require("fs");
const { format } = require("date-fns");

// Email Configuration
const transporter = nodemailer.createTransport({
  host: process.env.EMAIL_HOST,
  port: process.env.EMAIL_PORT,
  secure: false,
  auth: {
    user: process.env.EMAIL_USER,
    pass: process.env.EMAIL_PASS,
  },
});

function formatDate(value) {
  if (!value) return "N/A";
  const dateValue = new Date(value);
  return isNaN(dateValue.getTime()) ? value : format(dateValue, "dd-MM-yyyy");
}

function extractYearNumber(yearString) {
  const match = yearString.match(/\d+/); // Extract numeric part
  return match ? parseInt(match[0], 10) : 0; // Convert to integer
}

async function sendDailyRenewalFeeEmail() {
  try {
    await sequelize.authenticate();
    console.log("Database connected successfully.");

    const today = new Date();
    const startOfDay = new Date(
      today.getFullYear(),
      today.getMonth(),
      today.getDate()
    );
    const endOfDay = new Date(
      today.getFullYear(),
      today.getMonth(),
      today.getDate(),
      23,
      59,
      59
    );

    console.log("Fetching renewal records for:", formatDate(startOfDay));

    // Fetch applications with due renewals
    const applicationsData = await Applications.findAll({
      where: {
        due_date_of_next_renewal: {
          [Op.between]: [startOfDay, endOfDay],
        },
      },
      attributes: [
        "application_number",
        "patent_number",
        "applicant_name",
        "due_date_of_next_renewal",
        "legal_patent_status",
      ],
      order: [["due_date_of_next_renewal", "ASC"]],
    });

    if (applicationsData.length === 0) {
      console.log("No renewals due today.");

      // Prepare Email Content for no dues
      let emailContent = "<h2>Patent Renewal Fees Due Today</h2>";
      emailContent += `<h3>Date: ${formatDate(today)}</h3>`;
      emailContent +=
        "<p style='font-size: 16px; color: #666; margin: 20px 0;'><strong>No patent renewals are due today.</strong></p>";

      // Send Email
      const mailOptions = {
        from: process.env.EMAIL_FROM,
        to: process.env.EMAIL_TO,
        subject: `Patent Renewal Fees Due Today - ${formatDate(today)}`,
        html: emailContent,
      };

      const info = await transporter.sendMail(mailOptions);
      console.log("Daily renewal fee email sent successfully:", info.messageId);
      return;
    }

    // Calculate extension date (3 months from due date)
    const calculateExtensionDate = (dueDate) => {
      if (!dueDate) return null;
      const date = new Date(dueDate);
      return new Date(date.setMonth(date.getMonth() + 6));
    };

    // Create table HTML function
    const createTableHTML = (title) => {
      return `
        <h3>${title}</h3>
        <table border="1" style="border-collapse: collapse; width: 100%; margin-bottom: 20px;">
          <tr>
            <th>Application Number</th>
            <th>Patent Number</th>
            <th>Applicant Name</th>
            <th>Legal Status</th>
            <th>Normal Due Date</th>
            <th>Due Date with Extension</th>
          </tr>
      `;
    };

    // Separate the data into two arrays
    const normalEntries = applicationsData.filter(
      (app) => app.legal_patent_status !== "Under Extension Period"
    );
    const extensionEntries = applicationsData.filter(
      (app) => app.legal_patent_status === "Under Extension Period"
    );

    // Prepare Email Content
    let emailContent = "<h2>Patent Renewal Fees Due Today</h2>";
    emailContent += `<h3>Date: ${formatDate(today)}</h3>`;

    // Create normal entries table
    if (normalEntries.length > 0) {
      emailContent += createTableHTML("Normal Renewal Due Applications");
      normalEntries.forEach((app) => {
        emailContent += `<tr>
          <td>${app.application_number}</td>
          <td>${app.patent_number || "N/A"}</td>
          <td>${app.applicant_name || "N/A"}</td>
          <td>${app.legal_patent_status || "N/A"}</td>
          <td>${formatDate(app.due_date_of_next_renewal)}</td>
          <td>${formatDate(
            calculateExtensionDate(app.due_date_of_next_renewal)
          )}</td>
        </tr>`;
      });
      emailContent += "</table>";
    }

    // Create extension period table
    if (extensionEntries.length > 0) {
      emailContent += createTableHTML("Applications Under Extension Period");
      extensionEntries.forEach((app) => {
        emailContent += `<tr>
          <td>${app.application_number}</td>
          <td>${app.patent_number || "N/A"}</td>
          <td>${app.applicant_name || "N/A"}</td>
          <td>${app.legal_patent_status || "N/A"}</td>
          <td>${formatDate(app.due_date_of_next_renewal)}</td>
          <td>${formatDate(
            calculateExtensionDate(app.due_date_of_next_renewal)
          )}</td>
        </tr>`;
      });
      emailContent += "</table>";
    }

    // Create Excel files
    const currentDate = formatDate(today).replace(/-/g, "_");
    const attachments = [];

    // Create Excel for normal entries
    if (normalEntries.length > 0) {
      const normalExcelPath = `./Normal_Renewal_Due_Applications_${currentDate}.xlsx`;
      const normalWorkbook = XLSX.utils.book_new();
      const normalSheetData = [
        [
          "Application Number",
          "Patent Number",
          "Applicant Name",
          "Legal Status",
          "Normal Due Date",
          "Due Date with Extension",
        ],
        ...normalEntries.map((app) => [
          app.application_number,
          app.patent_number || "N/A",
          app.applicant_name || "N/A",
          app.legal_patent_status || "N/A",
          formatDate(app.due_date_of_next_renewal),
          formatDate(calculateExtensionDate(app.due_date_of_next_renewal)),
        ]),
      ];

      const normalWorksheet = XLSX.utils.aoa_to_sheet(normalSheetData);
      XLSX.utils.book_append_sheet(
        normalWorkbook,
        normalWorksheet,
        "Normal Renewals" // Shortened sheet name
      );
      XLSX.writeFile(normalWorkbook, normalExcelPath);
      attachments.push({
        filename: `Normal_Renewal_Due_${currentDate}.xlsx`,
        path: normalExcelPath,
        contentType:
          "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
      });
    }

    // Create Excel for extension entries
    if (extensionEntries.length > 0) {
      const extensionExcelPath = `./Extension_Period_Applications_${currentDate}.xlsx`;
      const extensionWorkbook = XLSX.utils.book_new();
      const extensionSheetData = [
        [
          "Application Number",
          "Patent Number",
          "Applicant Name",
          "Legal Status",
          "Normal Due Date",
          "Due Date with Extension",
        ],
        ...extensionEntries.map((app) => [
          app.application_number,
          app.patent_number || "N/A",
          app.applicant_name || "N/A",
          app.legal_patent_status || "N/A",
          formatDate(app.due_date_of_next_renewal),
          formatDate(calculateExtensionDate(app.due_date_of_next_renewal)),
        ]),
      ];

      const extensionWorksheet = XLSX.utils.aoa_to_sheet(extensionSheetData);
      XLSX.utils.book_append_sheet(
        extensionWorkbook,
        extensionWorksheet,
        "Extension Period" // Shortened sheet name
      );
      XLSX.writeFile(extensionWorkbook, extensionExcelPath);
      attachments.push({
        filename: `Extension_Period_${currentDate}.xlsx`,
        path: extensionExcelPath,
        contentType:
          "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
      });
    }

    // Send Email
    const mailOptions = {
      from: process.env.EMAIL_FROM,
      // to: `<EMAIL>`,
      // cc: `<EMAIL>, <EMAIL>, <EMAIL>`,
      // to: `<EMAIL>`,
      subject: `Patent Renewal Fees Due Today - ${formatDate(today)}`,
      html: emailContent,
      attachments: attachments,
    };

    const info = await transporter.sendMail(mailOptions);
    console.log("Daily renewal fee email sent successfully:", info.messageId);

    // Clean up: Delete the temporary Excel files
    attachments.forEach((attachment) => {
      fs.unlinkSync(attachment.path);
    });
  } catch (error) {
    console.error("Error sending daily renewal fee email:", error);
  }
}

// Schedule to run daily at 9 AM IST
const ScheduleDailyRenewalFeeEmail = () => {
  console.log("Scheduled daily renewal fee email job running at 9 AM IST.");
  cron.schedule(
    "0 9 * * *", // Runs at 9 AM daily
    async () => {
      try {
        await sendDailyRenewalFeeEmail();
        console.log("Daily renewal fee email job completed.");
      } catch (error) {
        console.error("Error in scheduled daily renewal fee email job:", error);
      }
    },
    {
      timezone: "Asia/Kolkata",
    }
  );
};

// For testing
// sendDailyRenewalFeeEmail();

module.exports = {
  ScheduleDailyRenewalFeeEmail,
  // sendDailyRenewalFeeEmail,
};
