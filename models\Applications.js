const { DataTypes, Model } = require("sequelize");
const sequelize = require("../utils/database");

class Applications extends Model {
  static associate(models) {
    Applications.hasMany(models.Renewal, {
      foreignKey: "application_number",
      sourceKey: "application_number",
      as: "renewal",
    });
  }
}

Applications.init(
  {
    application_number: {
      type: DataTypes.STRING(255),
      primaryKey: true,
    },
    rkd_status: {
      type: DataTypes.STRING(255),
      allowNull: true,
    },
    application_status: {
      type: DataTypes.STRING(255),
      allowNull: true,
    },
    application_type: {
      type: DataTypes.STRING(200),
      allowNull: true,
    },
    date_of_filing: {
      type: DataTypes.DATE,
      allowNull: true,
    },
    title_of_invention: {
      type: DataTypes.TEXT,
      allowNull: true,
    },
    field_of_invention: {
      type: DataTypes.STRING(300),
      allowNull: true,
    },
    publication_date_u_s_11a: {
      type: DataTypes.DATE,
      allowNull: true,
    },
    priority_date: {
      type: DataTypes.DATE,
      allowNull: true,
    },
    publication_number: {
      type: DataTypes.STRING(400),
      allowNull: true,
    },
    publication_type: {
      type: DataTypes.STRING(255),
      allowNull: true,
    },
    priority_number: {
      type: DataTypes.STRING(500),
      allowNull: true,
    },
    priority_country: {
      type: DataTypes.STRING(300),
      allowNull: true,
    },
    classification: {
      type: DataTypes.TEXT,
      allowNull: true,
    },
    abstract: {
      type: DataTypes.TEXT,
      allowNull: true,
    },
    complete_specification: {
      type: DataTypes.TEXT("medium"),
      allowNull: true,
    },
    applicant_name: {
      type: DataTypes.TEXT,
      allowNull: true,
    },
    email_as_per_record: {
      type: DataTypes.STRING(400),
      allowNull: true,
    },
    additional_email: {
      type: DataTypes.STRING(400),
      allowNull: true,
    },
    email_updated_online: {
      type: DataTypes.STRING(400),
      allowNull: true,
    },
    pct_international_application_number: {
      type: DataTypes.STRING(200),
      allowNull: true,
    },
    pct_international_filing_date: {
      type: DataTypes.DATE,
      allowNull: true,
    },
    parent_application_number: {
      type: DataTypes.STRING(300),
      allowNull: true,
    },
    parent_application_filing_date: {
      type: DataTypes.DATE,
      allowNull: true,
    },
    request_for_examination_date: {
      type: DataTypes.DATE,
      allowNull: true,
    },
    first_examination_report_date: {
      type: DataTypes.DATE,
      allowNull: true,
    },
    date_of_cert_issue: {
      type: DataTypes.DATE,
      allowNull: true,
    },
    post_grant_journal_date: {
      type: DataTypes.DATE,
      allowNull: true,
    },
    reply_to_fer_date: {
      type: DataTypes.DATE,
      allowNull: true,
    },
    legal_patent_status: {
      type: DataTypes.STRING(800),
      allowNull: true,
    },
    patent_number: {
      type: DataTypes.STRING(255),
      allowNull: true,
    },
    due_date_of_next_renewal: {
      type: DataTypes.DATE,
      allowNull: true,
    },
    due_date_with_extension: {
      type: DataTypes.DATE,
      allowNull: true,
    },
    date_of_grant: {
      type: DataTypes.DATE,
      allowNull: true,
    },
    date_of_patent: {
      type: DataTypes.DATE,
      allowNull: true,
    },
    address_of_service: {
      type: DataTypes.TEXT("medium"),
      allowNull: true,
    },
    appropriate_office: {
      type: DataTypes.STRING(300),
      allowNull: true,
    },
    date_of_cessation: {
      type: DataTypes.DATE,
      allowNull: true,
    },
    toScrape: {
      type: DataTypes.BOOLEAN,
      allowNull: true,
    },
  },
  {
    sequelize,
    modelName: "Applications",
    tableName: "applications",
    timestamps: false,
  }
);

module.exports = Applications;
